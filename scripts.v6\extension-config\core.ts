/**
 * @fileoverview 插件配置管理模块的核心处理逻辑
 * @description 实现 defineExtensionConfig 函数、配置验证器和配置扁平化合并
 */

import { merge } from 'lodash-es';
import { createLogger } from '../shared/logger.js';
import type {
  ExtensionConfig,
  VariantConfig,
  ExtensionVariantsConfig,
  BaseVariantConfig,
  ValidationResult,
  WebstoreType,
  VariantType,
  ManifestVersionType,
  VariantChannel,
  WebstoreCNType,
} from './types.js';
import {
  SUPPORT_WEBSTORES,
  SUPPORT_VARIANTS,
  SUPPORT_MANIFEST_VERSIONS,
  WEBSTORE_CN,
} from './types.js';

const logger = createLogger('ExtensionConfig');

// #region --- 配置验证器 ---

/**
 * @description 验证单个变体配置
 * @param variant 变体配置
 * @param index 变体在数组中的索引
 */
function validateVariantConfig(variant: VariantConfig, index: number): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  const prefix = `variants[${index}]`;

  // 验证必填字段
  if (!variant.variantId) {
    errors.push(`${prefix} 缺少必填字段: variantId`);
  }
  if (!variant.variantName) {
    errors.push(`${prefix} 缺少必填字段: variantName`);
  }
  if (!variant.variantType) {
    errors.push(`${prefix} 缺少必填字段: variantType`);
  }
  if (!variant.webstore) {
    errors.push(`${prefix} 缺少必填字段: webstore`);
  }

  // 验证枚举值
  if (variant.variantType && !SUPPORT_VARIANTS.includes(variant.variantType)) {
    errors.push(
      `${prefix} 不支持的 variantType: ${variant.variantType}，支持的类型: ${SUPPORT_VARIANTS.join(', ')}`,
    );
  }
  if (variant.webstore && !SUPPORT_WEBSTORES.includes(variant.webstore)) {
    errors.push(
      `${prefix} 不支持的 webstore: ${variant.webstore}，支持的商店: ${SUPPORT_WEBSTORES.join(', ')}`,
    );
  }
  if (variant.manifestVersion && !SUPPORT_MANIFEST_VERSIONS.includes(variant.manifestVersion)) {
    errors.push(
      `${prefix} 不支持的 manifestVersion: ${variant.manifestVersion}，支持的版本: ${SUPPORT_MANIFEST_VERSIONS.join(', ')}`,
    );
  }

  // 验证 i18n.locales 手动设置警告
  if (variant.i18n?.locales && variant.i18n.locales.length > 0) {
    warnings.push(`${prefix} i18n.locales 字段会被自动扫描覆盖，手动设置将被忽略`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

// #endregion

// #region --- 配置处理核心逻辑 ---

/**
 * @description 生成变体渠道标识
 * @param webstore 浏览器商店
 * @param variantType 变体类型
 */
function generateVariantChannel(webstore: WebstoreType, variantType: VariantType): VariantChannel {
  return variantType === 'offline' ? `${webstore}_offline` : webstore;
}

/**
 * @description 生成变体目标标识
 * @param webstore 浏览器商店
 * @param manifestVersion Manifest 版本
 * @param variantType 变体类型
 */
function generateVariantTarget(
  webstore: WebstoreType,
  manifestVersion: ManifestVersionType,
  variantType: VariantType,
): string {
  return `${webstore}-mv${manifestVersion}-${variantType}`;
}

/**
 * @description 解析 Manifest 版本号
 * @param ext 扩展配置对象
 * @param variant 变体配置对象
 * @returns Manifest 版本号
 *
 * 优先级顺序（从高到低）：
 * 1. variant.manifest.manifestVersion
 * 2. variant.manifestVersion
 * 3. ext.manifest.manifestVersion
 * 4. ext.manifestVersion
 * 5. 默认值 3
 */
function resolveManifestVersion(ext: ExtensionConfig, variant: VariantConfig): ManifestVersionType {
  return (
    variant.manifest?.manifestVersion ??
    variant.manifestVersion ??
    ext.manifest?.manifestVersion ??
    ext.manifestVersion ??
    3
  );
}

/**
 * @description 解析默认语言
 * @param ext 扩展配置对象
 * @param variant 变体配置对象
 * @returns 默认语言代码
 *
 * 优先级顺序（从高到低）：
 * 1. variant.manifest.defaultLocale
 * 2. variant.defaultLocale
 * 3. ext.manifest.defaultLocale
 * 4. ext.defaultLocale
 * 5. 默认值 'en'
 */
function resolveDefaultLocale(ext: ExtensionConfig, variant: VariantConfig): string {
  return (
    variant.manifest?.defaultLocale ??
    variant.defaultLocale ??
    ext.manifest?.defaultLocale ??
    ext.defaultLocale ??
    'en'
  );
}

function resolveMeasurementId(ext: ExtensionConfig, variant: VariantConfig): string | undefined {
  return variant.measurementId ?? ext.measurementId;
}

/**
 * @description 简单处理单个变体配置（仅做基础合并，不生成路径）
 * @param config 原始扩展配置
 * @param variant 变体配置
 */
function mergeBaseVariantConfig(
  config: ExtensionConfig,
  variant: VariantConfig,
): BaseVariantConfig {
  // 确定核心元数据
  const manifestVersion = resolveManifestVersion(config, variant);
  const defaultLocale = resolveDefaultLocale(config, variant);
  const measurementId = resolveMeasurementId(config, variant);

  // 生成标识符
  const variantTarget = generateVariantTarget(
    variant.webstore,
    manifestVersion,
    variant.variantType,
  );
  const variantChannel = generateVariantChannel(variant.webstore, variant.variantType);
  const webstoreCN = WEBSTORE_CN[variant.webstore] as WebstoreCNType;

  // 浅层合并配置
  const mergedI18n = merge({}config.i18n, variant.i18n);
  const mergedManifest = merge({}config.manifest, variant.manifest);

  return {
    name: config.name,
    version: config.version,
    manifestVersion,
    defaultLocale,
    variantId: variant.variantId,
    variantName: variant.variantName,
    variantType: variant.variantType,
    variantChannel,
    webstoreCN,
    webstore: variant.webstore,
    webstoreId: variant.webstoreId,
    webstoreUrl: variant.webstoreUrl,
    measurementId,
    variantTarget,
    i18n: mergedI18n,
    manifest: mergedManifest,
  };
}

/**
 * @description 在内存中处理 extension.config.ts，返回所有处理完成的基础变体配置
 * @param config 从 extension.config.ts 文件中导入的原始配置对象
 * @returns ExtensionVariantsConfig 对象，包含扩展基本信息和所有变体配置
 */
export function defineExtensionConfig(config: ExtensionConfig): ExtensionVariantsConfig {
  logger.verbose(`开始处理扩展配置: ${config.name}`);

  // 1. 验证基础必填字段
  if (!config.name) {
    const errorMessage = '配置中缺少必填字段: name';
    logger.error(errorMessage);
    throw new Error(errorMessage);
  }
  if (!config.version) {
    const errorMessage = '配置中缺少必填字段: version';
    logger.error(errorMessage);
    throw new Error(errorMessage);
  }
  if (!config.variants || config.variants.length === 0) {
    const errorMessage = '配置中缺少必填字段: variants，或 variants 数组为空';
    logger.error(errorMessage);
    throw new Error(errorMessage);
  }

  // 验证顶层 manifestVersion
  if (config.manifestVersion && !SUPPORT_MANIFEST_VERSIONS.includes(config.manifestVersion)) {
    const errorMessage = `不支持的 manifestVersion: ${config.manifestVersion}，支持的版本: ${SUPPORT_MANIFEST_VERSIONS.join(', ')}`;
    logger.error(errorMessage);
    throw new Error(errorMessage);
  }

  // 检查顶层 i18n.locales 手动设置警告
  if (config.i18n?.locales && config.i18n.locales.length > 0) {
    logger.warn('i18n.locales 字段会被自动扫描覆盖，手动设置将被忽略');
  }

  // 2. 处理每个变体配置
  const processedVariants: Record<string, BaseVariantConfig> = {};
  const variantTargets = new Set<string>();
  const allWarnings: string[] = [];

  config.variants.forEach((variant, index) => {
    // 验证单个变体配置
    const validation = validateVariantConfig(variant, index);

    // 收集警告
    allWarnings.push(...validation.warnings);

    // 如果有错误，抛出异常
    if (!validation.isValid) {
      const errorMessage = `变体配置验证失败:\n${validation.errors.join('\n')}`;
      logger.error(errorMessage);
      throw new Error(errorMessage);
    }

    // 简单处理变体配置
    const processedVariant = mergeBaseVariantConfig(config, variant);

    // 验证 variantTarget 唯一性
    if (variantTargets.has(processedVariant.variantTarget)) {
      const errorMessage = `重复的 variantTarget: ${processedVariant.variantTarget}，请检查 webstore、manifestVersion 和 variantType 的组合`;
      logger.error(errorMessage);
      throw new Error(errorMessage);
    }

    variantTargets.add(processedVariant.variantTarget);
    processedVariants[processedVariant.variantTarget] = processedVariant;

    logger.verbose(`处理变体配置: ${processedVariant.variantTarget}`);
  });

  // 输出所有警告
  allWarnings.forEach((warning) => {
    logger.warn(warning);
  });

  logger.success(
    `扩展配置处理完成: ${config.name}，共 ${Object.keys(processedVariants).length} 个变体`,
  );

  return {
    name: config.name,
    version: config.version,
    variants: processedVariants,
  };
}

// #endregion
