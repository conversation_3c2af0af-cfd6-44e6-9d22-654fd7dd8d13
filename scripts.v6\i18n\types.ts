/**
 * @fileoverview i18n 模块的类型定义
 * @description 定义国际化处理相关的 TypeScript 类型
 */

import { SUPPORTED_LOCALES } from '@wxt-dev/i18n/build';

// #region --- 外部依赖导出 ---

/**
 * @description 从 @wxt-dev/i18n/build 导出支持的语言列表
 */
export { SUPPORTED_LOCALES };

// #endregion

// #region --- 基础类型定义 ---

/**
 * @description i18n 语言包消息
 */
export interface LocaleMessages {
  [key: string]: string;
}

/**
 * @description Chrome manifest 中使用的消息格式
 */
export interface ChromeMessage {
  message: string;
  description?: string;
  placeholders?: Record<string, { content: string; example?: string }>;
}

/**
 * @description Chrome 格式的语言包
 */
export interface ChromeLocaleMessages {
  [key: string]: ChromeMessage;
}

/**
 * @description Vue I18n 格式的多语言消息集合
 */
export interface VueI18nMessages {
  [locale: string]: LocaleMessages;
}

/**
 * @description Chrome 格式的多语言消息集合
 */
export interface ChromeI18nMessages {
  [locale: string]: ChromeLocaleMessages;
}

// #endregion

// #region --- 配置类型 ---

/**
 * @description i18n 配置（从 extension-config 模块引用）
 */
export interface I18nConfig {
  /** 语言列表（会被自动扫描覆盖，手动设置会发出警告） */
  locales?: string[];
  /** 包含键模式的正则表达式数组 */
  includeKeys?: string[];
  /** 排除键模式的正则表达式数组 */
  excludeKeys?: string[];
  /** 仅生成 Chrome 语言包格式的语言代码列表 */
  chromeOnlyLocales?: string[];
  /** 在 Chrome 语言包中只包含这些 key 的正则表达式数组 */
  chromeOnlyKeys?: string[];
}

/**
 * @description 经过处理后的 i18n 配置
 */
export interface ProcessedI18nConfig {
  /** 支持的语言列表（通过扫描目录自动获取） */
  locales: string[];
  /** 包含键模式的正则表达式数组 */
  includeKeys?: string[];
  /** 排除键模式的正则表达式数组 */
  excludeKeys?: string[];
  /** 仅生成 Chrome 语言包格式的语言代码列表 */
  chromeOnlyLocales?: string[];
  /** 在 Chrome 语言包中只包含这些 key 的正则表达式数组 */
  chromeOnlyKeys?: string[];
  /** Vue I18n 格式的语言包内容 */
  vueMessages: VueI18nMessages;
  /** Chrome 扩展格式的语言包内容 */
  chromeMessages: ChromeI18nMessages;
}

// #endregion

// #region --- 处理选项类型 ---

/**
 * @description 语言包处理选项
 */
export interface I18nProcessOptions {
  /** 扩展名称 */
  extensionName: string;
  /** 变体目标标识 */
  variantTarget: string;
  /** i18n 配置 */
  config: I18nConfig;
  /** 共享语言包目录路径 */
  sharedLocalesPath: string;
  /** 扩展专属语言包目录路径 */
  extensionLocalesPath: string;
}

/**
 * @description 语言包生成结果
 */
export interface I18nProcessResult {
  /** 处理后的 i18n 配置 */
  processedConfig: ProcessedI18nConfig;
  /** 发现的语言列表 */
  discoveredLocales: string[];
  /** 处理的消息总数 */
  totalMessages: number;
  /** 警告信息列表 */
  warnings: string[];
}

// #endregion

// #region --- 内部处理类型 ---

/**
 * @description 条件化键值解析信息
 */
export interface ConditionalKeyInfo {
  /** 原始键名 */
  originalKey: string;
  /** 基础键名（去除条件后缀） */
  baseKey: string;
  /** 条件标识符 */
  condition: string;
  /** 匹配优先级（数字越大优先级越高） */
  priority: number;
}

/**
 * @description 占位符信息
 */
export interface PlaceholderInfo {
  /** 占位符名称 */
  name: string;
  /** 在消息中的位置索引 */
  index: number;
  /** 示例值 */
  example?: string;
}

/**
 * @description 消息转换结果
 */
export interface MessageConversionResult {
  /** 转换后的消息内容 */
  message: string;
  /** 提取的占位符信息 */
  placeholders: PlaceholderInfo[];
}

// #endregion
